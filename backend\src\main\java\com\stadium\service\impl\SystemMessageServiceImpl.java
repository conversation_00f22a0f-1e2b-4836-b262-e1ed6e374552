package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stadium.entity.SystemMessage;
import com.stadium.entity.User;
import com.stadium.entity.UserMessage;
import com.stadium.mapper.SystemMessageMapper;
import com.stadium.mapper.UserMapper;
import com.stadium.mapper.UserMessageMapper;
import com.stadium.service.SystemMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统消息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemMessageServiceImpl extends ServiceImpl<SystemMessageMapper, SystemMessage>
        implements SystemMessageService {

    private final SystemMessageMapper systemMessageMapper;
    private final UserMessageMapper userMessageMapper;
    private final UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendSystemNotification(String title, String content, Integer receiverType,
            String receiverIds, Long senderId) {
        try {
            // 创建系统消息
            SystemMessage systemMessage = new SystemMessage();
            systemMessage.setTitle(title);
            systemMessage.setContent(content);
            systemMessage.setType(1); // 系统通知
            systemMessage.setSendType(1); // 站内消息
            systemMessage.setReceiverType(receiverType);
            systemMessage.setReceiverIds(receiverIds);
            systemMessage.setSenderId(senderId);
            systemMessage.setStatus(1); // 已发送
            systemMessage.setSentTime(LocalDateTime.now());
            systemMessage.setCreateTime(LocalDateTime.now());
            systemMessage.setUpdateTime(LocalDateTime.now());

            // 保存系统消息
            save(systemMessage);

            // 发送给用户
            return sendToUsers(systemMessage);
        } catch (Exception e) {
            log.error("发送系统通知失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendBookingReminder(Long userId, String title, String content, String businessId) {
        try {
            // 创建系统消息
            SystemMessage systemMessage = new SystemMessage();
            systemMessage.setTitle(title);
            systemMessage.setContent(content);
            systemMessage.setType(2); // 预订提醒
            systemMessage.setSendType(1); // 站内消息
            systemMessage.setReceiverType(2); // 指定用户
            systemMessage.setReceiverIds(userId.toString());
            systemMessage.setBusinessId(businessId);
            systemMessage.setBusinessType("booking");
            systemMessage.setStatus(1); // 已发送
            systemMessage.setSentTime(LocalDateTime.now());
            systemMessage.setCreateTime(LocalDateTime.now());
            systemMessage.setUpdateTime(LocalDateTime.now());

            // 保存系统消息
            save(systemMessage);

            // 发送给指定用户
            return sendToUser(systemMessage, userId);
        } catch (Exception e) {
            log.error("发送预订提醒失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendActivityNotification(String title, String content, Integer receiverType,
            String receiverIds, String businessId) {
        try {
            // 创建系统消息
            SystemMessage systemMessage = new SystemMessage();
            systemMessage.setTitle(title);
            systemMessage.setContent(content);
            systemMessage.setType(3); // 活动通知
            systemMessage.setSendType(1); // 站内消息
            systemMessage.setReceiverType(receiverType);
            systemMessage.setReceiverIds(receiverIds);
            systemMessage.setBusinessId(businessId);
            systemMessage.setBusinessType("activity");
            systemMessage.setStatus(1); // 已发送
            systemMessage.setSentTime(LocalDateTime.now());
            systemMessage.setCreateTime(LocalDateTime.now());
            systemMessage.setUpdateTime(LocalDateTime.now());

            // 保存系统消息
            save(systemMessage);

            // 发送给用户
            return sendToUsers(systemMessage);
        } catch (Exception e) {
            log.error("发送活动通知失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendPaymentNotification(Long userId, String title, String content, String orderNo) {
        try {
            // 创建系统消息
            SystemMessage systemMessage = new SystemMessage();
            systemMessage.setTitle(title);
            systemMessage.setContent(content);
            systemMessage.setType(4); // 支付通知
            systemMessage.setSendType(1); // 站内消息
            systemMessage.setReceiverType(2); // 指定用户
            systemMessage.setReceiverIds(userId.toString());
            systemMessage.setBusinessId(orderNo);
            systemMessage.setBusinessType("payment");
            systemMessage.setStatus(1); // 已发送
            systemMessage.setSentTime(LocalDateTime.now());
            systemMessage.setCreateTime(LocalDateTime.now());
            systemMessage.setUpdateTime(LocalDateTime.now());

            // 保存系统消息
            save(systemMessage);

            // 发送给指定用户
            return sendToUser(systemMessage, userId);
        } catch (Exception e) {
            log.error("发送支付通知失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendMaintenanceNotification(String title, String content) {
        try {
            // 创建系统消息
            SystemMessage systemMessage = new SystemMessage();
            systemMessage.setTitle(title);
            systemMessage.setContent(content);
            systemMessage.setType(5); // 维护通知
            systemMessage.setSendType(1); // 站内消息
            systemMessage.setReceiverType(1); // 全体用户
            systemMessage.setStatus(1); // 已发送
            systemMessage.setSentTime(LocalDateTime.now());
            systemMessage.setCreateTime(LocalDateTime.now());
            systemMessage.setUpdateTime(LocalDateTime.now());

            // 保存系统消息
            save(systemMessage);

            // 发送给所有用户
            return sendToUsers(systemMessage);
        } catch (Exception e) {
            log.error("发送维护通知失败", e);
            return false;
        }
    }

    @Override
    public boolean scheduleMessage(SystemMessage message, LocalDateTime scheduledTime) {
        try {
            message.setScheduledTime(scheduledTime);
            message.setStatus(0); // 草稿状态
            message.setCreateTime(LocalDateTime.now());
            message.setUpdateTime(LocalDateTime.now());
            return save(message);
        } catch (Exception e) {
            log.error("定时发送消息失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recallMessage(Long messageId, Long operatorId) {
        try {
            SystemMessage message = getById(messageId);
            if (message == null) {
                return false;
            }

            // 更新消息状态为已撤回
            message.setStatus(2);
            message.setUpdateTime(LocalDateTime.now());
            updateById(message);

            // 删除用户消息
            LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMessage::getMessageId, messageId);
            userMessageMapper.delete(wrapper);

            return true;
        } catch (Exception e) {
            log.error("撤回消息失败", e);
            return false;
        }
    }

    @Override
    public int getUnreadCount(Long userId) {
        LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessage::getUserId, userId)
                .eq(UserMessage::getIsRead, 0);
        return Math.toIntExact(userMessageMapper.selectCount(wrapper));
    }

    @Override
    public List<UserMessage> getUserMessages(Long userId, Integer type, Boolean isRead,
            int pageNum, int pageSize) {
        Page<UserMessage> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessage::getUserId, userId)
                .eq(type != null, UserMessage::getType, type)
                .eq(isRead != null, UserMessage::getIsRead, isRead ? 1 : 0)
                .orderByDesc(UserMessage::getSentTime);

        Page<UserMessage> result = userMessageMapper.selectPage(page, wrapper);
        return result.getRecords();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Long userId, Long messageId) {
        try {
            LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMessage::getUserId, userId)
                    .eq(UserMessage::getMessageId, messageId);

            UserMessage userMessage = userMessageMapper.selectOne(wrapper);
            if (userMessage != null) {
                userMessage.setIsRead(1);
                userMessage.setReadTime(LocalDateTime.now());
                userMessage.setUpdateTime(LocalDateTime.now());
                return userMessageMapper.updateById(userMessage) > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("标记消息为已读失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAllAsRead(Long userId, Integer type) {
        try {
            LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMessage::getUserId, userId)
                    .eq(UserMessage::getIsRead, 0)
                    .eq(type != null, UserMessage::getType, type);

            List<UserMessage> messages = userMessageMapper.selectList(wrapper);
            for (UserMessage message : messages) {
                message.setIsRead(1);
                message.setReadTime(LocalDateTime.now());
                message.setUpdateTime(LocalDateTime.now());
                userMessageMapper.updateById(message);
            }
            return true;
        } catch (Exception e) {
            log.error("批量标记消息为已读失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserMessage(Long userId, Long messageId) {
        try {
            LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMessage::getUserId, userId)
                    .eq(UserMessage::getMessageId, messageId);
            return userMessageMapper.delete(wrapper) > 0;
        } catch (Exception e) {
            log.error("删除用户消息失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleFavorite(Long userId, Long messageId) {
        try {
            LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMessage::getUserId, userId)
                    .eq(UserMessage::getMessageId, messageId);

            UserMessage userMessage = userMessageMapper.selectOne(wrapper);
            if (userMessage != null) {
                userMessage.setIsFavorite(!Boolean.TRUE.equals(userMessage.getIsFavorite()));
                userMessage.setUpdateTime(LocalDateTime.now());
                return userMessageMapper.updateById(userMessage) > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("收藏/取消收藏消息失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getMessageStatistics(Long userId) {
        Map<String, Object> statistics = new HashMap<>();

        // 总消息数
        LambdaQueryWrapper<UserMessage> totalWrapper = new LambdaQueryWrapper<>();
        totalWrapper.eq(UserMessage::getUserId, userId);
        long totalCount = userMessageMapper.selectCount(totalWrapper);
        statistics.put("totalCount", totalCount);

        // 未读消息数
        LambdaQueryWrapper<UserMessage> unreadWrapper = new LambdaQueryWrapper<>();
        unreadWrapper.eq(UserMessage::getUserId, userId)
                .eq(UserMessage::getIsRead, 0);
        long unreadCount = userMessageMapper.selectCount(unreadWrapper);
        statistics.put("unreadCount", unreadCount);

        // 收藏消息数
        LambdaQueryWrapper<UserMessage> favoriteWrapper = new LambdaQueryWrapper<>();
        favoriteWrapper.eq(UserMessage::getUserId, userId)
                .eq(UserMessage::getIsFavorite, true);
        long favoriteCount = userMessageMapper.selectCount(favoriteWrapper);
        statistics.put("favoriteCount", favoriteCount);

        return statistics;
    }

    @Override
    public void cleanExpiredMessages() {
        try {
            // 删除过期的系统消息
            LambdaQueryWrapper<SystemMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(SystemMessage::getExpireTime, LocalDateTime.now())
                    .isNotNull(SystemMessage::getExpireTime);

            List<SystemMessage> expiredMessages = list(wrapper);
            for (SystemMessage message : expiredMessages) {
                // 删除相关的用户消息
                LambdaQueryWrapper<UserMessage> userWrapper = new LambdaQueryWrapper<>();
                userWrapper.eq(UserMessage::getMessageId, message.getId());
                userMessageMapper.delete(userWrapper);

                // 删除系统消息
                removeById(message.getId());
            }

            log.info("清理过期消息完成，共清理{}条消息", expiredMessages.size());
        } catch (Exception e) {
            log.error("清理过期消息失败", e);
        }
    }

    @Override
    public List<SystemMessage> getSystemMessages(Integer type, Integer status, int pageNum, int pageSize) {
        Page<SystemMessage> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SystemMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(type != null, SystemMessage::getType, type)
                .eq(status != null, SystemMessage::getStatus, status)
                .orderByDesc(SystemMessage::getCreateTime);

        Page<SystemMessage> result = page(page, wrapper);
        return result.getRecords();
    }

    @Override
    public Map<String, Object> getSendStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> statistics = new HashMap<>();

        LambdaQueryWrapper<SystemMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SystemMessage::getSentTime, startTime, endTime)
                .eq(SystemMessage::getStatus, 1);

        List<SystemMessage> messages = list(wrapper);

        // 按类型统计
        Map<Integer, Long> typeCount = messages.stream()
                .collect(Collectors.groupingBy(SystemMessage::getType, Collectors.counting()));
        statistics.put("typeCount", typeCount);

        // 总发送数
        statistics.put("totalCount", messages.size());

        return statistics;
    }

    /**
     * 发送消息给用户
     */
    private boolean sendToUsers(SystemMessage systemMessage) {
        try {
            List<Long> userIds = getUserIds(systemMessage.getReceiverType(), systemMessage.getReceiverIds());

            for (Long userId : userIds) {
                sendToUser(systemMessage, userId);
            }
            return true;
        } catch (Exception e) {
            log.error("发送消息给用户失败", e);
            return false;
        }
    }

    /**
     * 发送消息给指定用户
     */
    private boolean sendToUser(SystemMessage systemMessage, Long userId) {
        try {
            UserMessage userMessage = new UserMessage();
            userMessage.setUserId(userId);
            userMessage.setMessageId(systemMessage.getId());
            userMessage.setTitle(systemMessage.getTitle());
            userMessage.setContent(systemMessage.getContent());
            userMessage.setType(systemMessage.getType());
            userMessage.setSenderId(systemMessage.getSenderId());
            userMessage.setSenderName(systemMessage.getSenderName());
            userMessage.setIsRead(0);
            userMessage.setIsTop(systemMessage.getIsTop());
            userMessage.setIsUrgent(systemMessage.getIsUrgent());
            userMessage.setIsFavorite(false);
            userMessage.setSentTime(systemMessage.getSentTime());
            userMessage.setCreateTime(LocalDateTime.now());
            userMessage.setUpdateTime(LocalDateTime.now());

            return userMessageMapper.insert(userMessage) > 0;
        } catch (Exception e) {
            log.error("发送消息给用户{}失败", userId, e);
            return false;
        }
    }

    /**
     * 根据接收者类型获取用户ID列表
     */
    private List<Long> getUserIds(Integer receiverType, String receiverIds) {
        switch (receiverType) {
            case 1: // 全体用户
                return userMapper.selectList(new LambdaQueryWrapper<User>()
                        .select(User::getId))
                        .stream()
                        .map(User::getId)
                        .collect(Collectors.toList());
            case 2: // 指定用户
                return Arrays.stream(receiverIds.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            case 3: // 指定角色（暂时返回空列表，需要根据实际角色系统实现）
                return Arrays.asList();
            default:
                return Arrays.asList();
        }
    }
}
