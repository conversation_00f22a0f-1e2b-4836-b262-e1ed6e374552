package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.stadium.entity.*;
import com.stadium.mapper.ActivityCheckInMapper;
import com.stadium.mapper.ActivityMapper;
import com.stadium.mapper.ActivityRegistrationMapper;
import com.stadium.service.ActivityCheckInStatisticsService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 活动签到统计服务实现类
 */
@Service
@RequiredArgsConstructor
public class ActivityCheckInStatisticsServiceImpl implements ActivityCheckInStatisticsService {

    private final ActivityMapper activityMapper;
    private final ActivityRegistrationMapper registrationMapper;
    private final ActivityCheckInMapper checkInMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "activity:check-in:statistics:";
    private static final long CACHE_EXPIRE_TIME = 5; // 缓存过期时间（分钟）

    @Override
    @Cacheable(value = "checkInTrend", key = "#activityId + ':' + #startTime + ':' + #endTime", unless = "#result == null")
    public List<ActivityCheckInTrend> getCheckInTrend(Long activityId, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取活动信息
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 获取所有签到记录
        List<ActivityCheckIn> checkIns = checkInMapper.selectList(
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .eq(ActivityCheckIn::getActivityId, activityId)
                        .between(ActivityCheckIn::getCheckInTime, startTime, endTime)
                        .orderByAsc(ActivityCheckIn::getCheckInTime));

        // 按小时统计签到人数
        Map<LocalDateTime, Integer> hourlyCount = new TreeMap<>();
        for (ActivityCheckIn checkIn : checkIns) {
            LocalDateTime hour = checkIn.getCheckInTime().withMinute(0).withSecond(0).withNano(0);
            hourlyCount.merge(hour, 1, Integer::sum);
        }

        // 计算累计签到人数和签到率
        List<ActivityCheckInTrend> trends = new ArrayList<>();
        int totalCheckInCount = 0;
        Long totalRegistrationCount = registrationMapper.selectCount(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getActivityId, activityId));

        ActivityCheckInTrend trend = new ActivityCheckInTrend();
        List<ActivityCheckInTrend.TimePoint> timePoints = new ArrayList<>();

        for (Map.Entry<LocalDateTime, Integer> entry : hourlyCount.entrySet()) {
            ActivityCheckInTrend.TimePoint timePoint = new ActivityCheckInTrend.TimePoint();
            timePoint.setTime(entry.getKey());
            timePoint.setCheckInCount(entry.getValue());
            totalCheckInCount += entry.getValue();
            timePoint.setTotalCheckInCount(totalCheckInCount);
            timePoint.setCheckInRate(totalRegistrationCount > 0
                    ? new BigDecimal(totalCheckInCount)
                            .divide(new BigDecimal(totalRegistrationCount), 4, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO);
            timePoints.add(timePoint);
        }

        trend.setTimePointsList(timePoints);
        trend.setTimePoints(timePoints);
        trends.add(trend);

        return trends;
    }

    @Override
    @Cacheable(value = "checkInMethodDistribution", key = "#activityId", unless = "#result == null")
    public ActivityCheckInMethodDistribution getCheckInMethodDistribution(Long activityId) {
        // 获取所有签到记录
        List<ActivityCheckIn> checkIns = checkInMapper.selectList(
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .eq(ActivityCheckIn::getActivityId, activityId));

        // 统计签到方式
        int onSiteCount = (int) checkIns.stream()
                .filter(c -> c.getCheckInType() == 1)
                .count();
        int qrCodeCount = (int) checkIns.stream()
                .filter(c -> c.getCheckInType() == 2)
                .count();
        int totalCount = checkIns.size();

        // 计算分布数据
        ActivityCheckInMethodDistribution distribution = new ActivityCheckInMethodDistribution();
        distribution.setOnSiteCount(onSiteCount);
        distribution.setQrCodeCount(qrCodeCount);
        distribution.setOnSiteRate(totalCount > 0
                ? new BigDecimal(onSiteCount).divide(new BigDecimal(totalCount), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO);
        distribution.setQrCodeRate(totalCount > 0
                ? new BigDecimal(qrCodeCount).divide(new BigDecimal(totalCount), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO);

        return distribution;
    }

    @Override
    @Cacheable(value = "checkInTimeDistribution", key = "#activityId", unless = "#result == null")
    public ActivityCheckInTimeDistribution getCheckInTimeDistribution(Long activityId) {
        // 获取所有签到记录
        List<ActivityCheckIn> checkIns = checkInMapper.selectList(
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .eq(ActivityCheckIn::getActivityId, activityId));

        // 按小时统计
        Map<Integer, Integer> hourlyDistribution = new HashMap<>();
        for (ActivityCheckIn checkIn : checkIns) {
            int hour = checkIn.getCheckInTime().getHour();
            hourlyDistribution.merge(hour, 1, Integer::sum);
        }

        // 按日期统计
        Map<String, Integer> dailyDistribution = new HashMap<>();
        for (ActivityCheckIn checkIn : checkIns) {
            String date = checkIn.getCheckInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dailyDistribution.merge(date, 1, Integer::sum);
        }

        // 计算高峰时段（签到人数最多的3个时段）
        List<String> peakHours = hourlyDistribution.entrySet().stream()
                .sorted(Map.Entry.<Integer, Integer>comparingByValue().reversed())
                .limit(3)
                .map(e -> String.format("%02d:00", e.getKey()))
                .collect(Collectors.toList());

        // 计算平均每小时签到人数
        BigDecimal averageHourlyCheckIn;
        if (hourlyDistribution.isEmpty()) {
            // 如果没有签到记录，平均值为0
            averageHourlyCheckIn = BigDecimal.ZERO;
        } else {
            // 只有在有签到记录时才计算平均值
            averageHourlyCheckIn = hourlyDistribution.values().stream()
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(hourlyDistribution.size()), 2, RoundingMode.HALF_UP);
        }

        // 构建返回数据
        ActivityCheckInTimeDistribution distribution = new ActivityCheckInTimeDistribution();
        distribution.setHourlyDistributionMap(hourlyDistribution);
        distribution.setDailyDistributionMap(dailyDistribution);
        distribution.setPeakHoursList(peakHours);
        distribution.setAverageHourlyCheckIn(averageHourlyCheckIn);

        return distribution;
    }

    @Override
    @Cacheable(value = "realTimeCheckInStatus", key = "#activityId", unless = "#result == null")
    public ActivityCheckInStatistics getRealTimeCheckInStatus(Long activityId) {
        // 获取活动信息
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 获取报名统计
        List<ActivityRegistration> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getActivityId, activityId));

        ActivityCheckInStatistics statistics = new ActivityCheckInStatistics();
        statistics.setActivityId(activityId);
        statistics.setTotalRegistrations(registrations.size());

        // 安全地计算已签到人数
        int checkedInCount = 0;
        for (ActivityRegistration registration : registrations) {
            if (registration.getCheckInStatus() != null && registration.getCheckInStatus() == 1) {
                checkedInCount++;
            }
        }

        statistics.setCheckedInCount(checkedInCount);
        statistics.setNotCheckedInCount(statistics.getTotalRegistrations() - statistics.getCheckedInCount());
        statistics.setCheckInRate(statistics.getTotalRegistrations() > 0
                ? new BigDecimal(statistics.getCheckedInCount())
                        .divide(new BigDecimal(statistics.getTotalRegistrations()), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO);

        // 获取签到方式统计
        List<Long> registrationIds = registrations.stream()
                .map(ActivityRegistration::getId)
                .collect(Collectors.toList());

        List<ActivityCheckIn> checkIns;
        if (registrationIds.isEmpty()) {
            // 如果没有报名记录，直接返回空列表，避免执行无效的SQL查询
            checkIns = new ArrayList<>();
        } else {
            // 只有在有报名记录时才执行查询
            checkIns = checkInMapper.selectList(
                    new LambdaQueryWrapper<ActivityCheckIn>()
                            .in(ActivityCheckIn::getRegistrationId, registrationIds));
        }

        statistics.setOnSiteCheckInCount((int) checkIns.stream()
                .filter(c -> c.getCheckInType() == 1)
                .count());
        statistics.setQrCodeCheckInCount((int) checkIns.stream()
                .filter(c -> c.getCheckInType() == 2)
                .count());

        return statistics;
    }

    /**
     * 清除指定活动的所有缓存
     *
     * @param activityId 活动ID
     */
    @CacheEvict(value = { "checkInTrend", "checkInMethodDistribution", "checkInTimeDistribution",
            "realTimeCheckInStatus" }, key = "#activityId")
    public void clearActivityCache(Long activityId) {
        // 清除所有相关的缓存
        String pattern = CACHE_KEY_PREFIX + activityId + ":*";
        Set<String> keys = redisTemplate.keys(pattern);
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 更新缓存
     *
     * @param activityId 活动ID
     */
    public void updateCache(Long activityId) {
        // 清除旧缓存
        clearActivityCache(activityId);

        // 更新实时状态缓存
        ActivityCheckInStatistics statistics = getRealTimeCheckInStatus(activityId);
        redisTemplate.opsForValue().set(
                CACHE_KEY_PREFIX + "realTime:" + activityId,
                statistics,
                CACHE_EXPIRE_TIME,
                TimeUnit.MINUTES);

        // 更新签到方式分布缓存
        ActivityCheckInMethodDistribution methodDistribution = getCheckInMethodDistribution(activityId);
        redisTemplate.opsForValue().set(
                CACHE_KEY_PREFIX + "method:" + activityId,
                methodDistribution,
                CACHE_EXPIRE_TIME,
                TimeUnit.MINUTES);

        // 更新签到时间分布缓存
        ActivityCheckInTimeDistribution timeDistribution = getCheckInTimeDistribution(activityId);
        redisTemplate.opsForValue().set(
                CACHE_KEY_PREFIX + "time:" + activityId,
                timeDistribution,
                CACHE_EXPIRE_TIME,
                TimeUnit.MINUTES);
    }

    @Override
    public String exportCheckInStatistics(Long activityId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 获取活动信息
            Activity activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw new RuntimeException("活动不存在");
            }

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();

            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建实时状态sheet
            Sheet statusSheet = workbook.createSheet("实时状态");
            createStatusSheet(statusSheet, headerStyle, activityId);

            // 创建签到趋势sheet
            Sheet trendSheet = workbook.createSheet("签到趋势");
            createTrendSheet(trendSheet, headerStyle, activityId, startTime, endTime);

            // 创建签到方式分布sheet
            Sheet methodSheet = workbook.createSheet("签到方式分布");
            createMethodSheet(methodSheet, headerStyle, activityId);

            // 创建签到时间分布sheet
            Sheet timeSheet = workbook.createSheet("签到时间分布");
            createTimeSheet(timeSheet, headerStyle, activityId);

            // 创建签到记录sheet
            Sheet recordSheet = workbook.createSheet("签到记录");
            createRecordSheet(recordSheet, headerStyle, activityId, startTime, endTime);

            // 保存文件
            String fileName = String.format("check_in_statistics_%d_%s.xlsx",
                    activityId,
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            Path filePath = Paths.get(System.getProperty("java.io.tmpdir"), fileName);

            try (FileOutputStream fileOut = new FileOutputStream(filePath.toFile())) {
                workbook.write(fileOut);
            }

            workbook.close();

            return filePath.toString();
        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    private void createStatusSheet(Sheet sheet, CellStyle headerStyle, Long activityId) {
        // 设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 15 * 256);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "指标", "数值" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取统计数据
        ActivityCheckInStatistics statistics = getRealTimeCheckInStatus(activityId);

        // 填充数据
        int rowNum = 1;
        createDataRow(sheet, rowNum++, "总报名人数", statistics.getTotalRegistrations());
        createDataRow(sheet, rowNum++, "已签到人数", statistics.getCheckedInCount());
        createDataRow(sheet, rowNum++, "未签到人数", statistics.getNotCheckedInCount());
        createDataRow(sheet, rowNum++, "签到率", String.format("%.2f%%",
                statistics.getCheckInRate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
        createDataRow(sheet, rowNum++, "现场签到人数", statistics.getOnSiteCheckInCount());
        createDataRow(sheet, rowNum++, "二维码签到人数", statistics.getQrCodeCheckInCount());
    }

    private void createTrendSheet(Sheet sheet, CellStyle headerStyle, Long activityId, LocalDateTime startTime,
            LocalDateTime endTime) {
        // 设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "时间点", "签到人数", "累计签到人数", "签到率" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取趋势数据
        List<ActivityCheckInTrend> trends = getCheckInTrend(activityId, startTime, endTime);
        List<ActivityCheckInTrend.TimePoint> timePoints = trends.stream()
                .map(ActivityCheckInTrend::getTimePoints)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 填充数据
        int rowNum = 1;
        for (ActivityCheckInTrend.TimePoint point : timePoints) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(point.getTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
            row.createCell(1).setCellValue(point.getCheckInCount());
            row.createCell(2).setCellValue(point.getTotalCheckInCount());
            row.createCell(3).setCellValue(String.format("%.2f%%",
                    point.getCheckInRate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
        }
    }

    private void createMethodSheet(Sheet sheet, CellStyle headerStyle, Long activityId) {
        // 设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 15 * 256);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "签到方式", "人数", "占比" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取方式分布数据
        ActivityCheckInMethodDistribution distribution = getCheckInMethodDistribution(activityId);

        // 填充数据
        int rowNum = 1;
        createDataRow(sheet, rowNum++, "现场签到", distribution.getOnSiteCount(),
                String.format("%.2f%%", distribution.getOnSiteRate().multiply(BigDecimal.valueOf(100)).setScale(2,
                        RoundingMode.HALF_UP)));
        createDataRow(sheet, rowNum++, "二维码签到", distribution.getQrCodeCount(),
                String.format("%.2f%%", distribution.getQrCodeRate().multiply(BigDecimal.valueOf(100)).setScale(2,
                        RoundingMode.HALF_UP)));
    }

    private void createTimeSheet(Sheet sheet, CellStyle headerStyle, Long activityId) {
        // 设置列宽
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "小时", "签到人数" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取时间分布数据
        ActivityCheckInTimeDistribution distribution = getCheckInTimeDistribution(activityId);
        Map<Integer, Integer> hourlyDistribution = distribution.getHourlyDistributionMap();

        // 填充数据
        int rowNum = 1;
        for (int hour = 0; hour < 24; hour++) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(String.format("%02d:00", hour));
            row.createCell(1).setCellValue(hourlyDistribution.getOrDefault(hour, 0));
        }
    }

    private void createRecordSheet(Sheet sheet, CellStyle headerStyle, Long activityId, LocalDateTime startTime,
            LocalDateTime endTime) {
        // 设置列宽
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 30 * 256);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "签到ID", "报名ID", "签到时间", "签到方式", "操作人", "备注" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取签到记录
        List<ActivityCheckIn> checkIns = checkInMapper.selectByActivityIdAndTimeRange(activityId, startTime, endTime);

        // 填充数据
        int rowNum = 1;
        for (ActivityCheckIn checkIn : checkIns) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(checkIn.getId());
            row.createCell(1).setCellValue(checkIn.getRegistrationId());
            row.createCell(2)
                    .setCellValue(checkIn.getCheckInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            row.createCell(3).setCellValue(checkIn.getCheckInType() == 1 ? "现场签到" : "二维码签到");
            row.createCell(4).setCellValue(checkIn.getOperatorId());
            row.createCell(5).setCellValue(checkIn.getRemark());
        }
    }

    private void createDataRow(Sheet sheet, int rowNum, String label, Object value) {
        Row row = sheet.createRow(rowNum);
        row.createCell(0).setCellValue(label);
        row.createCell(1).setCellValue(value.toString());
    }

    private void createDataRow(Sheet sheet, int rowNum, String label, Object value1, Object value2) {
        Row row = sheet.createRow(rowNum);
        row.createCell(0).setCellValue(label);
        row.createCell(1).setCellValue(value1.toString());
        row.createCell(2).setCellValue(value2.toString());
    }
}