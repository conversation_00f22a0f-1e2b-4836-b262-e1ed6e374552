package com.stadium;

import com.stadium.config.CorsProperties;
import com.stadium.config.JwtProperties;
import com.stadium.config.SpringDocProperties;
import com.stadium.config.StadiumSwaggerProperties;
import com.stadium.config.StadiumUploadProperties;
import com.stadium.config.properties.StadiumProperties;
import com.stadium.config.properties.TaskProperties;
import com.stadium.config.properties.CacheConfigProperties;
import com.stadium.config.properties.EncryptionProperties;
import com.stadium.config.properties.PerformanceProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@EnableConfigurationProperties({
        JwtProperties.class,
        CorsProperties.class,
        PerformanceProperties.class,
        SpringDocProperties.class,
        StadiumSwaggerProperties.class,
        StadiumUploadProperties.class,
        StadiumProperties.class,
        TaskProperties.class,
        CacheConfigProperties.class,
        EncryptionProperties.class
})
@ComponentScan(basePackages = "com.stadium", excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.stadium\\.common\\.config\\..*")
})
public class StadiumApplication {

    private static volatile boolean keepRunning = true;

    public static void main(String[] args) {
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            keepRunning = false;
            System.out.println("体育场馆管理系统正在关闭...");
        }));

        SpringApplication app = new SpringApplication(StadiumApplication.class);

        try {
            app.run(args);
            System.out.println("=== 体育场馆管理系统启动成功 ===");
            System.out.println("后端服务地址: http://localhost:8080/api");
            System.out.println("API文档地址: http://localhost:8080/api/doc.html");
            System.out.println("按 Ctrl+C 停止服务");

            // 保持应用运行
            while (keepRunning) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

        } catch (Exception e) {
            // 检查是否是ddlApplicationRunner相关错误且Tomcat已启动
            if (e.getMessage() != null && e.getMessage().contains("ddlApplicationRunner")) {
                System.out.println("=== 检测到ddlApplicationRunner错误，但Web服务器已启动 ===");
                System.out.println("后端服务地址: http://localhost:8080/api");
                System.out.println("API文档地址: http://localhost:8080/api/doc.html");
                System.out.println("按 Ctrl+C 停止服务");

                // 保持JVM运行，因为Web服务器实际上已经启动
                while (keepRunning) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } else {
                System.err.println("应用启动失败: " + e.getMessage());
                throw e;
            }
        }
    }
}