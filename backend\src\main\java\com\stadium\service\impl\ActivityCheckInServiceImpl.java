package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stadium.entity.Activity;
import com.stadium.entity.ActivityCheckIn;
import com.stadium.entity.ActivityCheckInStatistics;
import com.stadium.entity.ActivityRegistration;
import com.stadium.exception.BusinessException;
import com.stadium.mapper.ActivityCheckInMapper;
import com.stadium.mapper.ActivityMapper;
import com.stadium.mapper.ActivityRegistrationMapper;
import com.stadium.service.ActivityCheckInService;
import com.stadium.util.QrCodeUtil;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动签到服务实现类
 */
@Service
@RequiredArgsConstructor
public class ActivityCheckInServiceImpl extends ServiceImpl<ActivityCheckInMapper, ActivityCheckIn>
        implements ActivityCheckInService {

    private final ActivityCheckInMapper checkInMapper;
    private final ActivityMapper activityMapper;
    private final ActivityRegistrationMapper registrationMapper;
    private final QrCodeUtil qrCodeUtil;

    @Override
    public Page<ActivityCheckIn> page(Page<ActivityCheckIn> page, Long activityId, Long userId) {
        LambdaQueryWrapper<ActivityCheckIn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityCheckIn::getActivityId, activityId)
                .eq(userId != null, ActivityCheckIn::getUserId, userId)
                .orderByDesc(ActivityCheckIn::getCheckInTime);
        return page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityCheckIn checkIn(Long activityId, Long userId, Integer type, String remark) {
        // 检查是否已签到
        LambdaQueryWrapper<ActivityCheckIn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityCheckIn::getActivityId, activityId)
                .eq(ActivityCheckIn::getUserId, userId);
        if (count(wrapper) > 0) {
            throw new RuntimeException("该用户已签到");
        }

        // 创建签到记录
        ActivityCheckIn checkIn = new ActivityCheckIn();
        checkIn.setActivityId(activityId);
        checkIn.setUserId(userId);
        checkIn.setCheckInTime(LocalDateTime.now());
        checkIn.setCheckInType(type);
        checkIn.setRemark(remark);
        save(checkIn);

        return checkIn;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCheckIn(Long id) {
        // 检查签到记录是否存在
        ActivityCheckIn checkIn = getById(id);
        if (checkIn == null) {
            throw new RuntimeException("签到记录不存在");
        }
        removeById(id);
    }

    @Override
    public ActivityCheckInStats getCheckInStats(Long activityId) {
        // 查询所有签到记录
        LambdaQueryWrapper<ActivityCheckIn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityCheckIn::getActivityId, activityId);

        ActivityCheckInStats stats = new ActivityCheckInStats();
        stats.setTotalCount((int) count(wrapper));

        // 统计正常签到人数
        wrapper.eq(ActivityCheckIn::getCheckInType, 1);
        stats.setNormalCount((int) count(wrapper));

        // 统计迟到人数
        wrapper.clear();
        wrapper.eq(ActivityCheckIn::getActivityId, activityId)
                .eq(ActivityCheckIn::getCheckInType, 2);
        stats.setLateCount((int) count(wrapper));

        // 统计早退人数
        wrapper.clear();
        wrapper.eq(ActivityCheckIn::getActivityId, activityId)
                .eq(ActivityCheckIn::getCheckInType, 3);
        stats.setEarlyCount((int) count(wrapper));

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean checkIn(Long registrationId, Long operatorId, String remark) {
        // 检查报名记录是否存在
        ActivityRegistration registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            throw new BusinessException("报名记录不存在");
        }

        // 检查是否已签到
        if (registration.getCheckInStatus() == 1) {
            throw new BusinessException("该用户已签到");
        }

        // 创建签到记录
        ActivityCheckIn checkIn = new ActivityCheckIn();
        checkIn.setRegistrationId(registrationId);
        checkIn.setCheckInTime(LocalDateTime.now());
        checkIn.setCheckInType(1); // 现场签到
        checkIn.setOperatorId(operatorId);
        checkIn.setRemark(remark);
        checkIn.setCreateTime(LocalDateTime.now());
        checkIn.setUpdateTime(LocalDateTime.now());

        // 更新报名记录签到状态
        registration.setCheckInStatus(1);
        registration.setCheckInTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        return checkInMapper.insert(checkIn) > 0 && registrationMapper.updateById(registration) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean checkInByQrCode(Long registrationId, String qrCode) {
        // 验证二维码
        if (!verifyCheckInQrCode(qrCode)) {
            throw new BusinessException("无效的签到二维码");
        }

        // 检查报名记录是否存在
        ActivityRegistration registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            throw new BusinessException("报名记录不存在");
        }

        // 检查是否已签到
        if (registration.getCheckInStatus() == 1) {
            throw new BusinessException("该用户已签到");
        }

        // 创建签到记录
        ActivityCheckIn checkIn = new ActivityCheckIn();
        checkIn.setRegistrationId(registrationId);
        checkIn.setCheckInTime(LocalDateTime.now());
        checkIn.setCheckInType(2); // 扫码签到
        checkIn.setCreateTime(LocalDateTime.now());
        checkIn.setUpdateTime(LocalDateTime.now());

        // 更新报名记录签到状态
        registration.setCheckInStatus(1);
        registration.setCheckInTime(LocalDateTime.now());
        registration.setUpdateTime(LocalDateTime.now());

        return checkInMapper.insert(checkIn) > 0 && registrationMapper.updateById(registration) > 0;
    }

    @Override
    public Page<ActivityCheckIn> getActivityCheckIns(Long activityId, Page<ActivityCheckIn> page) {
        // 获取活动的所有报名记录ID
        List<Long> registrationIds = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getActivityId, activityId)
                        .select(ActivityRegistration::getId))
                .stream().map(ActivityRegistration::getId).collect(Collectors.toList());

        // 查询签到记录
        return checkInMapper.selectPage(page,
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .in(ActivityCheckIn::getRegistrationId, registrationIds)
                        .orderByDesc(ActivityCheckIn::getCheckInTime));
    }

    @Override
    public Page<ActivityCheckIn> getUserCheckIns(Long userId, Page<ActivityCheckIn> page) {
        // 获取用户的所有报名记录ID
        List<Long> registrationIds = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getUserId, userId)
                        .select(ActivityRegistration::getId))
                .stream().map(ActivityRegistration::getId).collect(Collectors.toList());

        // 查询签到记录
        return checkInMapper.selectPage(page,
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .in(ActivityCheckIn::getRegistrationId, registrationIds)
                        .orderByDesc(ActivityCheckIn::getCheckInTime));
    }

    @Override
    public ActivityCheckInStatistics getCheckInStatistics(Long activityId) {
        ActivityCheckInStatistics statistics = new ActivityCheckInStatistics();
        statistics.setActivityId(activityId);

        // 获取活动信息
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new BusinessException("活动不存在");
        }

        // 获取报名统计
        List<ActivityRegistration> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getActivityId, activityId));

        statistics.setTotalRegistrations(registrations.size());
        statistics.setCheckedInCount((int) registrations.stream()
                .filter(r -> r.getCheckInStatus() == 1)
                .count());
        statistics.setNotCheckedInCount(statistics.getTotalRegistrations() - statistics.getCheckedInCount());
        statistics.setCheckInRate(statistics.getTotalRegistrations() > 0
                ? new BigDecimal(statistics.getCheckedInCount())
                        .divide(new BigDecimal(statistics.getTotalRegistrations()), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO);

        // 获取签到方式统计
        List<Long> registrationIds = registrations.stream()
                .map(ActivityRegistration::getId)
                .collect(Collectors.toList());
        List<ActivityCheckIn> checkIns = checkInMapper.selectList(
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .in(ActivityCheckIn::getRegistrationId, registrationIds));

        statistics.setOnSiteCheckInCount((int) checkIns.stream()
                .filter(c -> c.getCheckInType() == 1)
                .count());
        statistics.setQrCodeCheckInCount((int) checkIns.stream()
                .filter(c -> c.getCheckInType() == 2)
                .count());

        return statistics;
    }

    @Override
    public String generateCheckInQrCode(Long activityId) {
        // 检查活动是否存在
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new BusinessException("活动不存在");
        }

        // 生成二维码内容
        String content = String.format("activity:%d:%d", activityId, System.currentTimeMillis());
        return qrCodeUtil.generateQrCode(content);
    }

    @Override
    public boolean verifyCheckInQrCode(String qrCode) {
        try {
            // 解码二维码内容
            String content = qrCodeUtil.decodeQrCode(qrCode);
            String[] parts = content.split(":");

            // 验证格式
            if (parts.length != 3 || !"activity".equals(parts[0])) {
                return false;
            }

            // 验证活动是否存在
            Long activityId = Long.parseLong(parts[1]);
            Activity activity = activityMapper.selectById(activityId);
            if (activity == null) {
                return false;
            }

            // 验证二维码是否过期（24小时）
            long timestamp = Long.parseLong(parts[2]);
            return System.currentTimeMillis() - timestamp <= 24 * 60 * 60 * 1000;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String exportActivityCheckIns(Long activityId) {
        // 获取活动信息
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new BusinessException("活动不存在");
        }

        // 获取所有签到记录
        List<ActivityCheckIn> checkIns = checkInMapper.selectList(
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .eq(ActivityCheckIn::getActivityId, activityId)
                        .orderByDesc(ActivityCheckIn::getCheckInTime));

        // 生成Excel文件
        String fileName = String.format("activity_check_in_%d_%s.xlsx",
                activityId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("签到记录");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("签到ID");
            headerRow.createCell(1).setCellValue("报名ID");
            headerRow.createCell(2).setCellValue("签到时间");
            headerRow.createCell(3).setCellValue("签到方式");
            headerRow.createCell(4).setCellValue("操作人ID");
            headerRow.createCell(5).setCellValue("备注");

            // 填充数据
            int rowNum = 1;
            for (ActivityCheckIn checkIn : checkIns) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(checkIn.getId());
                row.createCell(1).setCellValue(checkIn.getRegistrationId());
                row.createCell(2).setCellValue(
                        checkIn.getCheckInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                row.createCell(3).setCellValue(checkIn.getCheckInType() == 1 ? "现场签到" : "扫码签到");
                row.createCell(4).setCellValue(checkIn.getOperatorId());
                row.createCell(5).setCellValue(checkIn.getRemark());
            }

            // 自动调整列宽
            for (int i = 0; i < 6; i++) {
                sheet.autoSizeColumn(i);
            }

            // 保存文件
            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }

            return filePath;
        } catch (IOException e) {
            throw new BusinessException("导出签到记录失败: " + e.getMessage());
        }
    }

    @Override
    public String exportUserCheckIns(Long userId) {
        // 获取用户的所有报名记录ID
        List<Long> registrationIds = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getUserId, userId)
                        .select(ActivityRegistration::getId))
                .stream().map(ActivityRegistration::getId).collect(Collectors.toList());

        // 获取签到记录
        List<ActivityCheckIn> checkIns = checkInMapper.selectList(
                new LambdaQueryWrapper<ActivityCheckIn>()
                        .in(ActivityCheckIn::getRegistrationId, registrationIds)
                        .orderByDesc(ActivityCheckIn::getCheckInTime));

        // 生成Excel文件
        String fileName = String.format("user_check_in_%d_%s.xlsx",
                userId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("签到记录");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("签到ID");
            headerRow.createCell(1).setCellValue("报名ID");
            headerRow.createCell(2).setCellValue("签到时间");
            headerRow.createCell(3).setCellValue("签到方式");
            headerRow.createCell(4).setCellValue("操作人ID");
            headerRow.createCell(5).setCellValue("备注");

            // 填充数据
            int rowNum = 1;
            for (ActivityCheckIn checkIn : checkIns) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(checkIn.getId());
                row.createCell(1).setCellValue(checkIn.getRegistrationId());
                row.createCell(2).setCellValue(
                        checkIn.getCheckInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                row.createCell(3).setCellValue(checkIn.getCheckInType() == 1 ? "现场签到" : "扫码签到");
                row.createCell(4).setCellValue(checkIn.getOperatorId());
                row.createCell(5).setCellValue(checkIn.getRemark());
            }

            // 自动调整列宽
            for (int i = 0; i < 6; i++) {
                sheet.autoSizeColumn(i);
            }

            // 保存文件
            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }

            return filePath;
        } catch (IOException e) {
            throw new BusinessException("导出签到记录失败: " + e.getMessage());
        }
    }
}