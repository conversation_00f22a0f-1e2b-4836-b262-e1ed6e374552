package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stadium.dto.VenueFacilityDTO;
import com.stadium.dto.VenueFacilityQueryDTO;
import com.stadium.entity.Venue;
import com.stadium.entity.VenueFacility;
import com.stadium.exception.BusinessException;
import com.stadium.mapper.VenueFacilityMapper;
import com.stadium.mapper.VenueMapper;
import com.stadium.service.VenueFacilityService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 场馆设施服务实现类
 */
@Service
public class VenueFacilityServiceImpl extends ServiceImpl<VenueFacilityMapper, VenueFacility>
        implements VenueFacilityService {

    private final VenueMapper venueMapper;

    // 设施类型映射
    private static final Map<Integer, String> TYPE_MAP = new HashMap<>();
    // 设施状态映射
    private static final Map<Integer, String> STATUS_MAP = new HashMap<>();

    static {
        TYPE_MAP.put(1, "照明");
        TYPE_MAP.put(2, "空调");
        TYPE_MAP.put(3, "音响");
        TYPE_MAP.put(4, "座椅");
        TYPE_MAP.put(5, "其他");

        STATUS_MAP.put(0, "维护中");
        STATUS_MAP.put(1, "可用");
    }

    public VenueFacilityServiceImpl(VenueMapper venueMapper) {
        this.venueMapper = venueMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VenueFacility createVenueFacility(VenueFacilityDTO venueFacilityDTO) {
        // 检查场馆是否存在
        Venue venue = venueMapper.selectById(venueFacilityDTO.getVenueId());
        if (venue == null) {
            throw new BusinessException("场馆不存在");
        }

        // 创建场馆设施
        VenueFacility facility = new VenueFacility();
        BeanUtils.copyProperties(venueFacilityDTO, facility);
        facility.setCreateTime(LocalDateTime.now());
        facility.setUpdateTime(LocalDateTime.now());

        // 保存场馆设施
        save(facility);
        return facility;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VenueFacility updateVenueFacility(Long id, VenueFacilityDTO venueFacilityDTO) {
        // 检查场馆设施是否存在
        VenueFacility existingFacility = getById(id);
        if (existingFacility == null) {
            throw new BusinessException("场馆设施不存在");
        }

        // 检查场馆是否存在
        Venue venue = venueMapper.selectById(venueFacilityDTO.getVenueId());
        if (venue == null) {
            throw new BusinessException("场馆不存在");
        }

        // 更新场馆设施
        BeanUtils.copyProperties(venueFacilityDTO, existingFacility);
        existingFacility.setUpdateTime(LocalDateTime.now());
        updateById(existingFacility);

        return existingFacility;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVenueFacility(Long id) {
        // 检查场馆设施是否存在
        VenueFacility facility = getById(id);
        if (facility == null) {
            throw new BusinessException("场馆设施不存在");
        }

        // 删除场馆设施
        removeById(id);
    }

    @Override
    public VenueFacility getVenueFacility(Long id) {
        VenueFacility facility = getById(id);
        if (facility == null) {
            throw new BusinessException("场馆设施不存在");
        }
        return facility;
    }

    @Override
    public Page<VenueFacility> listVenueFacilities(Integer current, Integer size, String keyword) {
        Page<VenueFacility> page = new Page<>(current, size);
        LambdaQueryWrapper<VenueFacility> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(keyword)) {
            wrapper.like(VenueFacility::getName, keyword)
                    .or()
                    .like(VenueFacility::getDescription, keyword);
        }
        wrapper.orderByDesc(VenueFacility::getCreateTime);
        return page(page, wrapper);
    }

    @Override
    public Page<VenueFacilityDTO> page(VenueFacilityQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<VenueFacility> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(queryDTO.getVenueId() != null, VenueFacility::getVenueId, queryDTO.getVenueId())
                .like(StringUtils.hasText(queryDTO.getName()), VenueFacility::getName, queryDTO.getName())
                .eq(queryDTO.getType() != null, VenueFacility::getType, queryDTO.getType())
                .eq(queryDTO.getStatus() != null, VenueFacility::getStatus, queryDTO.getStatus())
                .orderByDesc(VenueFacility::getCreateTime);

        // 分页查询
        Page<VenueFacility> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<VenueFacility> facilityPage = page(page, wrapper);

        // 转换为DTO
        Page<VenueFacilityDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(facilityPage, dtoPage, "records");

        // 查询场馆信息
        Map<Long, Venue> venueMap = new HashMap<>();
        facilityPage.getRecords().forEach(facility -> {
            if (!venueMap.containsKey(facility.getVenueId())) {
                Venue venue = venueMapper.selectById(facility.getVenueId());
                venueMap.put(facility.getVenueId(), venue);
            }
        });

        // 转换为DTO
        dtoPage.setRecords(facilityPage.getRecords().stream()
                .map(facility -> convertToDTO(facility, venueMap.get(facility.getVenueId())))
                .collect(Collectors.toList()));

        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VenueFacilityDTO updateStatus(Long id, Integer status) {
        // 检查场馆设施是否存在
        VenueFacility facility = getById(id);
        if (facility == null) {
            throw new BusinessException("场馆设施不存在");
        }

        // 更新状态
        facility.setStatus(status);
        facility.setUpdateTime(LocalDateTime.now());
        updateById(facility);

        // 查询场馆信息
        Venue venue = venueMapper.selectById(facility.getVenueId());

        // 转换为DTO
        return convertToDTO(facility, venue);
    }

    /**
     * 将实体转换为DTO
     */
    private VenueFacilityDTO convertToDTO(VenueFacility facility, Venue venue) {
        VenueFacilityDTO dto = new VenueFacilityDTO();
        BeanUtils.copyProperties(facility, dto);

        // 设置场馆名称
        if (venue != null) {
            dto.setVenueName(venue.getName());
        }

        // 设置类型名称
        dto.setTypeName(TYPE_MAP.get(facility.getType()));

        // 设置状态名称
        dto.setStatusName(STATUS_MAP.get(facility.getStatus()));

        return dto;
    }
}