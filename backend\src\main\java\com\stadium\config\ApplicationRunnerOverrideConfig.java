package com.stadium.config;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * ApplicationRunner覆盖配置
 * 用于解决MyBatis-Plus的ddlApplicationRunner Bean冲突问题
 */
@Configuration
public class ApplicationRunnerOverrideConfig {

    /**
     * 创建一个正确的ApplicationRunner Bean来覆盖MyBatis-Plus的ddlApplicationRunner
     */
    @Bean("ddlApplicationRunner")
    @Primary
    public ApplicationRunner ddlApplicationRunner() {
        return new ApplicationRunner() {
            @Override
            public void run(ApplicationArguments args) throws Exception {
                // 空实现，不执行任何DDL操作
                // 这样可以避免MyBatis-Plus的ddlApplicationRunner Bean冲突
            }
        };
    }
}
